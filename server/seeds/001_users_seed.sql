-- Seed data for users table
-- This file contains sample users for development and testing

-- Insert sample users (passwords are hashed for 'password123')
INSERT INTO users (email, password_hash, first_name, last_name, notification_enabled, preferred_language) VALUES
('<EMAIL>', '$2a$12$5gfCX9lNNw8WN/nxusTTFOnIeAwq8vr3Qc9fnIwJoCgrbSEt0IxXi', '<PERSON>', 'Do<PERSON>', true, 'en'),
('<EMAIL>', '$2a$12$5gfCX9lNNw8WN/nxusTTFOnIeAwq8vr3Qc9fnIwJoCgrbSEt0IxXi', 'Jane', 'Smith', true, 'en'),
('<EMAIL>', '$2a$12$5gfCX9lNNw8WN/nxusTTFOnIeAwq8vr3Qc9fnIwJoCgrbSEt0IxXi', 'Admin', 'User', true, 'en'),
('<EMAIL>', '$2a$12$5gfCX9lNNw8WN/nxusTTFOnIeAwq8vr3Qc9fnIwJoCgrbSEt0IxXi', 'Test', 'User', false, 'af')
ON CONFLICT (email) DO NOTHING;
